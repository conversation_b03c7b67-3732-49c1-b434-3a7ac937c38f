package com.demo.nacosspringcloudconsumer.controller;

import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.demo.nacosspringcloudconsumer.feignService.ProviderFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * @Name ConsumerController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/6 10:57
 */
@RestController
public class ConsumerController {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ProviderFeignService providerFeignService;

    @GetMapping("/test")
    public String echo(String param) {
//        String result = restTemplate.getForObject("http://nacos-provider/provider/test", String.class);
        String result = providerFeignService.test(param);
        System.out.println(result);
        return result;
    }
}
