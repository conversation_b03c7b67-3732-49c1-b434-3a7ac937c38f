package com.demo.nacosspringcloudconsumer.feignService;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Name ProviderFeignService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/8 15:46
 */
@FeignClient(name = "nacos-provider", path = "/provider")
public interface ProviderFeignService {

    @GetMapping("/test")
    public String test(String param);
}
